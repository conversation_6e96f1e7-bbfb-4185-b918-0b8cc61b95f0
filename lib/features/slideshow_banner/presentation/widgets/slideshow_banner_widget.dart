import 'package:flutter/material.dart';
import 'dart:async';

class SlideshowBannerWidget extends StatefulWidget {
  const SlideshowBannerWidget({super.key});

  @override
  State<SlideshowBannerWidget> createState() => _SlideshowBannerWidgetState();
}

class _SlideshowBannerWidgetState extends State<SlideshowBannerWidget> {
  late PageController _pageController;
  late Timer _timer;
  int _currentPage = 0;

  final List<BannerItem> _bannerItems = [
    BannerItem(imageUrl: 'https://picsum.photos/800/300?random=1'),
    BannerItem(imageUrl: 'https://picsum.photos/800/300?random=2'),
    BannerItem(imageUrl: 'https://picsum.photos/800/300?random=3'),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);
    _startAutoSlide();
  }

  @override
  void dispose() {
    _timer.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _startAutoSlide() {
    _timer = Timer.periodic(const Duration(seconds: 3), (timer) {
      if (_pageController.hasClients) {
        _currentPage = (_currentPage + 1) % _bannerItems.length;
        _pageController.animateToPage(
          _currentPage,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Container(
        height: 200,
        margin: const EdgeInsets.all(16.0),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12.0),
          child: Stack(
            children: [
              PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index % _bannerItems.length;
                  });
                },
                itemBuilder: (context, index) {
                  final item = _bannerItems[index % _bannerItems.length];
                  return _BannerItemWidget(item: item);
                },
              ),
              // Page indicators
              Positioned(
                bottom: 12,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _bannerItems.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color:
                            _currentPage == index
                                ? Colors.white
                                : Colors.white.withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _BannerItemWidget extends StatelessWidget {
  final BannerItem item;

  const _BannerItemWidget({required this.item});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.3),
            Colors.black.withOpacity(0.7),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background image
          Image.network(
            item.imageUrl,
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              // Fallback to a colored container if image fails to load
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.blue.shade400, Colors.purple.shade400],
                  ),
                ),
                child: const Icon(Icons.image, size: 50, color: Colors.white54),
              );
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                width: double.infinity,
                height: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [Colors.grey.shade300, Colors.grey.shade400],
                  ),
                ),
                child: const Center(child: CircularProgressIndicator()),
              );
            },
          ),
          // Overlay gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.transparent, Colors.black.withOpacity(0.4)],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class BannerItem {
  final String imageUrl;

  BannerItem({required this.imageUrl});
}
